# ESP32 MicroPython Escape Room - Game Mechanics and Architecture

## Overview

This is a collaborative two-team escape room game built using ESP32 microcontrollers running MicroPython. The system creates an immersive puzzle experience where two teams must work together to escape within a time limit.

## System Architecture

### Hardware Components

**Team A ESP32 (Code Generator & Weight Sensor)**
- **ESP32 Nano** microcontroller
- **20x4 LCD Display** (I2C address 0x27)
- **Pressure/Weight Sensor** (analog input via GPIO3)
- **Start Button** (GPIO2 with pull-up)
- **Status LED** (GPIO4)
- **Buzzer** (PWM on GPIO5)

**Team B ESP32 (Code Input & Weight Display)**
- **ESP32 Nano** microcontroller  
- **20x4 LCD Display** (I2C address 0x27)
- **Three Rotary Switches** for 3-digit code input (hundreds, tens, ones)
- **Confirm Button** (GPIO13 with pull-up)
- **Status LED** (GPIO14)
- **Buzzer** (PWM on GPIO7)

### Network Infrastructure

- **WiFi Network**: Both ESP32s connect to local WiFi
- **MQTT Broker**: Central message broker (IP: **************:1883)
- **Node-RED Dashboard**: Web-based monitoring and control interface
- **HTTP Endpoints**: `/admin`, `/team-a`, `/team-b` for different interfaces

## Game Flow and Mechanics

### Phase 1: Initialization and Ready State

1. **Boot Sequence**:
   - Both ESP32s connect to WiFi network
   - Initialize hardware components (LCD, sensors, buttons)
   - Connect to MQTT broker
   - Display welcome screen

2. **Ready State**:
   - Teams press their respective buttons when ready to start
   - System waits for BOTH teams to be ready
   - 3-second countdown begins once both teams are ready
   - Game automatically starts after countdown

### Phase 2: Stage 1 - Code Generation and Entry

**Team A (Code Generator)**:
- Generates a random 3-digit code (100-999)
- Displays the code on their LCD screen
- Must communicate this code to Team B through external means
- Monitors Team B's code attempts via MQTT

**Team B (Code Entry)**:
- Uses three rotary switches to input 3-digit codes
- Has 5 attempts maximum to enter the correct code
- Each attempt is sent via MQTT to Team A for verification
- Receives feedback on remaining attempts

**Stage 1 Success Condition**:
- Team B enters the correct code within 5 attempts
- System automatically advances to Stage 2

**Stage 1 Failure Condition**:
- Team B exhausts all 5 attempts with wrong codes
- Game ends in failure state

### Phase 3: Stage 2 - Weight Challenge

**Team A (Weight Sensor)**:
- Generates a random target weight (1kg to 10kg in 250g increments)
- Monitors pressure sensor for weight placement
- Displays current weight reading on LCD
- Requires weight to be held within ±500g tolerance for 1 second

**Team B (Weight Display)**:
- Receives target weight from Team A via MQTT
- Displays the target weight on LCD screen
- Must communicate target weight to Team A through external means

**Stage 2 Success Condition**:
- Team A places correct weight (within tolerance) on sensor
- Weight must be held steady for 1 full second
- Game completes successfully with total time displayed

### Phase 4: Game Completion

**Success State**:
- Both teams' displays show victory message
- Total completion time is calculated and displayed
- Celebration sounds play on both devices
- Confetti animation on Node-RED dashboard

**Failure State**:
- Both teams' displays show failure message
- Game can be reset for another attempt

## Game States

Each ESP32 maintains the following game states:

1. **"waiting"** - Initial state, waiting for team to press ready button
2. **"ready"** - Team is ready, waiting for other team
3. **"stage1"** - Code generation/entry phase active
4. **"stage2"** - Weight challenge phase active  
5. **"completed"** - Game successfully completed
6. **"failed"** - Game failed (too many wrong attempts)

## Communication Requirements

The game is designed to require **external communication** between teams:

- **Stage 1**: Team A must verbally/physically communicate the generated code to Team B
- **Stage 2**: Team B must verbally/physically communicate the target weight to Team A

This creates the collaborative puzzle aspect where teams cannot succeed without working together and communicating effectively.

## Technical Implementation Highlights

### Hardware Abstraction
- Modular hardware classes for sensors, displays, and input devices
- Configurable pin assignments via `hardware_config.py`
- Error handling for hardware initialization failures

### Network Resilience
- Automatic WiFi connection with retry logic
- MQTT connection with fallback to local testing mode
- Graceful degradation when network components fail

### Real-time Updates
- 20Hz main loop for responsive hardware interaction
- Periodic status publishing every 2 seconds
- Immediate MQTT messaging for critical game events

### User Experience
- Clear LCD feedback for all game states
- Audio feedback via buzzers for different events
- LED status indicators with different blink patterns
- Debounced button inputs for reliable operation

## Scalability and Extensibility

The architecture supports easy expansion:
- Additional game stages can be added
- New hardware components can be integrated
- Multiple team pairs can run simultaneously
- Custom puzzle logic can be implemented

This modular design makes the system suitable for educational purposes, team building exercises, and entertainment applications.
