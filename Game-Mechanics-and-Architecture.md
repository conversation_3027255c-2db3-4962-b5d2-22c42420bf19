# ESP32 MicroPython Escape Room - "Escape Room for Two" Project Analysis

## Project Overview

This is a **2CI :: TEINN :: Node-Red Project** implementing a collaborative escape room experience called "Escape room for two". The system creates an immersive puzzle where two teams in separate physical rooms must cooperate using severely limited communication to complete sequential challenges.

## Original Design Intent vs Current Implementation

### Original Specification Requirements:
- **Communication**: 2-character message limit via web interface only
- **Physical Separation**: Teams in separate rooms with no direct communication
- **Laptop Access**: Restricted website interface for each team
- **Stage 2 Target**: Fixed 10kg weight requirement

### Current Implementation Differences:
- **Communication**: External verbal/physical communication (not web-restricted)
- **Weight Target**: Random weight generation (1-10kg) instead of fixed 10kg
- **Interface**: Direct ESP32 interaction rather than laptop-mediated

## System Architecture

### Hardware Components

**Team A ESP32 (Code Generator & Weight Sensor)**
- **ESP32 Nano** microcontroller
- **20x4 LCD Display** (I2C address 0x27)
- **Pressure/Weight Sensor** (analog input via GPIO3)
- **Start Button** (GPIO2 with pull-up) - *Initiates game sequence*
- **Status LED** (GPIO4) - *Green LED for success indication*
- **Piezo Buzzer** (PWM on GPIO5) - *Audio feedback system*

**Team B ESP32 (Code Input & Weight Display)**
- **ESP32 Nano** microcontroller
- **20x4 LCD Display** (I2C address 0x27)
- **Three Rotary Switches** for 3-digit code input (hundreds, tens, ones)
- **Confirm Button** (GPIO13 with pull-up) - *Manual code confirmation*
- **Status LED** (GPIO14) - *Green LED for success indication*
- **Piezo Buzzer** (PWM on GPIO7) - *Audio feedback system*

### Network Infrastructure

- **WiFi Network**: Both ESP32s connect to local WiFi
- **MQTT Broker**: Central message broker (IP: **************:1883)
- **Node-RED Dashboard**: Web-based monitoring and control interface
- **HTTP Endpoints**: `/admin`, `/team-a`, `/team-b` for different team interfaces
- **Real-time Communication**: MQTT pub/sub for inter-device coordination

## Game Flow and Mechanics

### Phase 1: Initialization and Ready State

1. **Boot Sequence**:
   - Both ESP32s connect to WiFi network
   - Initialize hardware components (LCD, sensors, buttons)
   - Connect to MQTT broker for real-time coordination
   - Display welcome screen on both team displays

2. **Ready State**:
   - Teams press their respective buttons when ready to start
   - System waits for BOTH teams to be ready (collaborative requirement)
   - 3-second countdown begins once both teams are ready
   - Game automatically starts after countdown

### Phase 2: Stage 1 - Code Relay with Rotary Switches

**Team A (Code Generator & Game Initiator)**:
- **Starts the game** by pressing the "Start" button on ESP32
- **Generates random 3-digit code** (100-999) automatically
- **Displays code on LCD screen** for team to see
- **Monitors Team B's attempts** via MQTT messages
- **Provides attempt feedback** (remaining attempts counter)

**Team B (Code Entry & Decoder)**:
- **Receives code information** through limited communication channel
- **Uses three rotary switches** to input 3-digit codes (hundreds, tens, ones)
- **Has 5 attempts maximum** to enter the correct code
- **Manual confirmation required** via confirm button
- **Real-time feedback** on remaining attempts

**Stage 1 Success Condition**:
- Team B enters the correct code within 5 attempts
- **Green LED lights up in both rooms** (original spec requirement)
- **Short piezo sound plays** indicating success (original spec requirement)
- System automatically advances to Stage 2

**Stage 1 Failure Condition**:
- Team B exhausts all 5 attempts with wrong codes
- Game ends in failure state with error indication

### Phase 3: Stage 2 - Weight-Based Challenge

**Team B (Weight Target Display)**:
- **Receives weight challenge message** on LCD display
- **Shows target weight requirement** (currently random 1-10kg vs original 10kg fixed)
- **Original spec format**: "Press for 10 kg / Currently XY kg"
- **Must communicate target** to Team A through limited channel

**Team A (Weight Sensor & Physical Challenge)**:
- **Monitors pressure/weight sensor** connected to ESP32
- **Displays current weight reading** on LCD in real-time
- **Requires precise weight application** (±500g tolerance for 1 second)
- **Physical challenge**: Apply correct pressure to reach target

**Stage 2 Success Condition**:
- Team A applies correct weight (within tolerance) to sensor
- Weight must be held steady for 1 full second
- **Final green LED activated in both rooms** (original spec requirement)
- **Long piezo sound indicates completion** (original spec requirement)

### Phase 4: Game Completion and Dashboard

**Success State**:
- Both teams' displays show victory message
- **Total completion time calculated and displayed**
- **Centralized Node-RED dashboard tracks progress** (original spec requirement)
- Celebration sounds play on both devices
- **Dashboard shows total time for all stages** (original spec requirement)

**Game Reset Capability**:
- **Organizers can reset system via dashboard** (original spec requirement)
- **Prepares system for next session** (original spec requirement)
- All game states return to initial conditions

## Game States

Each ESP32 maintains the following game states:

1. **"waiting"** - Initial state, waiting for team to press ready button
2. **"ready"** - Team is ready, waiting for other team
3. **"stage1"** - Code generation/entry phase active
4. **"stage2"** - Weight challenge phase active  
5. **"completed"** - Game successfully completed
6. **"failed"** - Game failed (too many wrong attempts)

## Communication Requirements

The game is designed to require **external communication** between teams:

- **Stage 1**: Team A must verbally/physically communicate the generated code to Team B
- **Stage 2**: Team B must verbally/physically communicate the target weight to Team A

This creates the collaborative puzzle aspect where teams cannot succeed without working together and communicating effectively.

## Technical Implementation Highlights

### Hardware Abstraction
- Modular hardware classes for sensors, displays, and input devices
- Configurable pin assignments via `hardware_config.py`
- Error handling for hardware initialization failures

### Network Resilience
- Automatic WiFi connection with retry logic
- MQTT connection with fallback to local testing mode
- Graceful degradation when network components fail

### Real-time Updates
- 20Hz main loop for responsive hardware interaction
- Periodic status publishing every 2 seconds
- Immediate MQTT messaging for critical game events

### User Experience
- Clear LCD feedback for all game states
- Audio feedback via buzzers for different events
- LED status indicators with different blink patterns
- Debounced button inputs for reliable operation

## Scalability and Extensibility

The architecture supports easy expansion:
- Additional game stages can be added
- New hardware components can be integrated
- Multiple team pairs can run simultaneously
- Custom puzzle logic can be implemented

This modular design makes the system suitable for educational purposes, team building exercises, and entertainment applications.
